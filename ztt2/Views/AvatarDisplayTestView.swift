//
//  AvatarDisplayTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 头像显示测试视图
 * 用于验证大转盘界面中用户头像是否正确显示
 */
struct AvatarDisplayTestView: View {
    
    @EnvironmentObject var dataManager: DataManager
    @State private var selectedMember: Member?
    @State private var showWheelLottery = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("头像显示测试")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.top, 20)
                
                // 说明文字
                Text("测试大转盘界面中用户头像是否正确显示角色头像")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                
                // 成员选择
                memberSelectionSection
                
                // 测试按钮
                if selectedMember != nil {
                    Button(action: {
                        showWheelLottery = true
                    }) {
                        HStack {
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 20))
                            Text("打开大转盘测试头像显示")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(DesignSystem.Colors.primary)
                        .cornerRadius(12)
                    }
                }
                
                // 头像映射说明
                avatarMappingSection
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationBarHidden(true)
        }
        .fullScreenCover(isPresented: $showWheelLottery) {
            if let member = selectedMember {
                LotteryWheelView(
                    member: member,
                    onDismiss: {
                        showWheelLottery = false
                    }
                )
            }
        }
    }
    
    // MARK: - 成员选择区域
    
    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择测试成员")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(dataManager.members, id: \.objectID) { member in
                    memberCard(member: member)
                }
            }
        }
        .padding(.horizontal, 16)
    }
    
    private func memberCard(member: Member) -> some View {
        VStack(spacing: 8) {
            // 头像 - 使用修复后的avatarImageName
            Image(member.avatarImageName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 50, height: 50)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(
                            selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary : Color.gray.opacity(0.3),
                            lineWidth: 2
                        )
                )
            
            // 姓名和角色
            VStack(spacing: 2) {
                Text(member.displayName)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineLimit(1)
                
                Text(member.roleDisplayName)
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            
            // 积分
            Text("\(Int(member.currentPoints))积分")
                .font(.system(size: 10, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(selectedMember?.objectID == member.objectID ? DesignSystem.Colors.primary.opacity(0.1) : Color.gray.opacity(0.05))
        )
        .onTapGesture {
            selectedMember = member
        }
    }
    
    // MARK: - 头像映射说明
    
    private var avatarMappingSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("头像资源映射")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            VStack(alignment: .leading, spacing: 8) {
                mappingRow(role: "父亲", avatar: "爸爸头像")
                mappingRow(role: "母亲", avatar: "妈妈头像")
                mappingRow(role: "儿子", avatar: "男生头像")
                mappingRow(role: "女儿", avatar: "女生头像")
                mappingRow(role: "其他", avatar: "其他头像")
            }
            .padding(16)
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
    }
    
    private func mappingRow(role: String, avatar: String) -> some View {
        HStack {
            Text(role)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .frame(width: 60, alignment: .leading)
            
            Text("→")
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text(avatar)
                .font(.system(size: 14))
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Spacer()
            
            // 显示实际头像
            Image(avatar)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)
                .clipShape(Circle())
        }
    }
}

#Preview {
    AvatarDisplayTestView()
        .environmentObject(DataManager.shared)
}
